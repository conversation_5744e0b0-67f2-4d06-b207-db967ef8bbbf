import type { FC } from 'react';
import { styled } from '@mui/material/styles';

interface ChatContactProps {
  name: string;
  isActive?: boolean;
  status: string;
  avatarColor?: string;
  onClick?: () => void;
}

const ChatContactContainer = styled('div')<{ active?: boolean }>(({ active }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: '10px 15px',
  cursor: 'pointer',
  borderBottom: '1px solid #333',
  backgroundColor: active ? '#1e1e1e' : 'transparent',
}));

const Avatar = styled('div')<{ avatarColor?: string }>(({ avatarColor }) => ({
  width: 40,
  height: 40,
  borderRadius: '50%',
  backgroundColor: avatarColor || '#0078d4',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginRight: 10,
  fontWeight: 'bold',
  color: 'white',
  fontSize: 18,
}));

const ContactInfo = styled('div')({
  flex: 1,
});

const ContactName = styled('div')({
  fontWeight: 'bold',
  marginBottom: 3,
});

const ContactStatus = styled('div')({
  fontSize: 12,
  color: '#aaa',
});

const ChatContact: FC<ChatContactProps> = ({ name, isActive = false, status, avatarColor, onClick }) => {
  return (
    <ChatContactContainer active={isActive} onClick={onClick}>
      <Avatar avatarColor={avatarColor}>{name.charAt(0)}</Avatar>
      <ContactInfo>
        <ContactName>{name}</ContactName>
        <ContactStatus>{status}</ContactStatus>
      </ContactInfo>
    </ChatContactContainer>
  );
};

export default ChatContact;