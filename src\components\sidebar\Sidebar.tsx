import type { FC } from 'react';
import { useState } from 'react';
import { styled } from '@mui/material/styles';
import ChatContact from './ChatContact';
import GlobalSettingsWindow from '../settings/GlobalSettingsWindow';

interface SidebarProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  onChatSelect?: (chatId: string) => void;
  selectedChatId?: string | null;
}

const SidebarContainer = styled('div')({
  width: 250,
  display: 'flex',
  flexDirection: 'column',
  borderRight: '1px solid #333',
  height: '100vh',
  background: '#181818',
});

const SidebarHeader = styled('div')({
  padding: '0 16px',
  borderBottom: '1px solid #e0e0e0',
});

const SidebarTabs = styled('div')({
  display: 'flex',
  gap: 8,
  padding: '16px 0 0 0',
});

const SidebarTab = styled('button')<{ active?: boolean }>(({ active }) => ({
  background: 'none',
  border: 'none',
  fontSize: '1rem',
  padding: '8px 16px',
  cursor: 'pointer',
  borderBottom: active ? '2px solid #4f8cff' : '2px solid transparent',
  color: active ? '#4f8cff' : '#555',
  fontWeight: 500,
  transition: 'border-color 0.2s, color 0.2s',
}));

const SidebarContacts = styled('div')({
  flex: 1,
  overflowY: 'auto',
});

const SidebarFooter = styled('div')({
  padding: 10,
  display: 'flex',
  flexDirection: 'column',
  gap: 10,
});

const NewChatButton = styled('button')({
  backgroundColor: '#0078d4',
  color: 'white',
  border: 'none',
  padding: 10,
  borderRadius: 4,
  cursor: 'pointer',
  fontWeight: 'bold',
});

const InstallButton = styled('button')({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: 5,
  backgroundColor: 'transparent',
  color: 'white',
  border: '1px solid #555',
  padding: 8,
  borderRadius: 4,
  cursor: 'pointer',
  fontSize: 12,
});

const Sidebar: FC<SidebarProps> = ({ activeTab, setActiveTab, onChatSelect, selectedChatId }) => {
  const [settingsOpen, setSettingsOpen] = useState(false);

  const handleChatClick = (chatId: string) => {
    if (onChatSelect) {
      onChatSelect(chatId);
    }
  };

  return (
    <SidebarContainer>
      <SidebarHeader>
        <SidebarTabs>
          <SidebarTab active={activeTab === 'CHATS'} onClick={() => setActiveTab('CHATS')}>
            CHATS
          </SidebarTab>
          <SidebarTab active={activeTab === 'AGENT FLOWS'} onClick={() => setActiveTab('AGENT FLOWS')}>
            AGENT FLOWS
          </SidebarTab>
        </SidebarTabs>
      </SidebarHeader>
      <SidebarContacts>
        {activeTab === 'CHATS' && (
          <ChatContact
            name="KalleBalleHangkuk"
            isActive={selectedChatId === 'kalleballehangkuk'}
            status="Chat öppnad"
            onClick={() => handleChatClick('kalleballehangkuk')}
          />
        )}
        {activeTab === 'AGENT FLOWS' && (
          <ChatContact
            name="Example Flow"
            isActive={true}
            status="Flow ready"
            avatarColor="#7b5cff" // purple/blue for flows
          />
        )}
      </SidebarContacts>
      <SidebarFooter>
        <NewChatButton>NEW AGENT</NewChatButton>
        <InstallButton onClick={() => setSettingsOpen(true)}>
          <span style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ verticalAlign: 'middle' }}>
              <path d="M19.14 12.94c.04-.3.06-.61.06-.94s-.02-.64-.06-.94l2.03-1.58a.5.5 0 00.12-.64l-1.92-3.32a.5.5 0 00-.61-.22l-2.39.96a7.03 7.03 0 00-1.62-.94l-.36-2.53A.5.5 0 0014 2h-4a.5.5 0 00-.5.42l-.36 2.53c-.59.23-1.14.54-1.62.94l-2.39-.96a.5.5 0 00-.61.22l-1.92 3.32a.5.5 0 00.12.64l2.03 1.58c-.04.3-.06.61-.06.94s.02.64.06.94l-2.03 1.58a.5.5 0 00-.12.64l1.92 3.32c.14.24.44.33.68.22l2.39-.96c.48.4 1.03.71 1.62.94l.36 2.53A.5.5 0 0010 22h4c.25 0 .46-.18.5-.42l.36-2.53c.59-.23 1.14-.54 1.62-.94l2.39.96c.24.1.54.01.68-.22l1.92-3.32a.5.5 0 00-.12-.64l-2.03-1.58zM12 15.5A3.5 3.5 0 1112 8.5a3.5 3.5 0 010 7z" fill="#888"/>
            </svg>
            OPTIONS
          </span>
        </InstallButton>
        <GlobalSettingsWindow open={settingsOpen} onClose={() => setSettingsOpen(false)} />
      </SidebarFooter>
    </SidebarContainer>
  );
};

export default Sidebar;