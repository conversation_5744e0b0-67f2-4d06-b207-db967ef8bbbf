import type { FC } from 'react';
import { styled } from '@mui/material/styles';
import WelcomeScreen from '../WelcomeScreen';
import MessageList from './MessageList';
import MessageInput from './MessageInput';

interface ChatWindowProps {
  selectedChatId?: string | null;
}

const ChatWindow: FC<ChatWindowProps> = ({ selectedChatId }) => {
  const isActiveChat = Boolean(selectedChatId);

  const ChatWindowContainer = styled('div')({
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
  });

  const ChatHeader = styled('div')({
    padding: 15,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottom: '1px solid #333',
  });

  const SimulateReplyButton = styled('button')({
    backgroundColor: '#0078d4',
    color: 'white',
    border: 'none',
    padding: '8px 15px',
    borderRadius: 4,
    cursor: 'pointer',
  });

  const getChatName = (chatId: string | null) => {
    switch (chatId) {
      case 'kalleballehangkuk':
        return 'KalleBalleHangkuk';
      default:
        return 'Unknown Chat';
    }
  };

  return (
    <ChatWindowContainer>
      {isActiveChat ? (
        <>
          <ChatHeader>
            <h3>{getChatName(selectedChatId)}</h3>
            <SimulateReplyButton onClick={() => console.log('Simulate reply')}>
              SIMULATE REPLY
            </SimulateReplyButton>
          </ChatHeader>
          <MessageList />
          <MessageInput />
        </>
      ) : (
        <WelcomeScreen onStartChat={() => console.log('Start chat from welcome screen')} />
      )}
    </ChatWindowContainer>
  );
};

export default ChatWindow;