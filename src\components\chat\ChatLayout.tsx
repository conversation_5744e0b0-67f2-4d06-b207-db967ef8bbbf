import { useState } from 'react';
import type { FC } from 'react';
import { styled } from '@mui/material/styles';
import Sidebar from '../sidebar/Sidebar';
import ChatWindow from './ChatWindow';
import ReactFlowWindow from '../flow/ReactFlowWindow';

const ChatLayout: FC = () => {
  const [activeTab, setActiveTab] = useState('CHATS');
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);

  const ChatLayoutContainer = styled('div')({
    display: 'flex',
    height: '100vh',
    backgroundColor: '#121212',
    color: 'white',
  });

  const handleChatSelect = (chatId: string) => {
    setSelectedChatId(chatId);
  };

  return (
    <ChatLayoutContainer>
      <Sidebar
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        onChatSelect={handleChatSelect}
        selectedChatId={selectedChatId}
      />
      {activeTab === 'CHATS' ? (
        <ChatWindow selectedChatId={selectedChatId} />
      ) : (
        <ReactFlowWindow />
      )}
    </ChatLayoutContainer>
  );
};

export default ChatLayout;