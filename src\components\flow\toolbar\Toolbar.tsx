import React from 'react';
import { styled } from '@mui/material/styles';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import SettingsIcon from '@mui/icons-material/Settings';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';

const ToolbarContainer = styled('div')({
  width: '100%',
  height: 62,
  background: 'rgba(40,60,110,0.15)',
  borderTop: '1.5px solid #3a3e48',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: 18,
  boxShadow: '0 -4px 18px 0 rgba(80,140,255,0.07)',
  zIndex: 11,
  backdropFilter: 'blur(8px)',
});

const ToolbarButton = styled('button')({
  background: 'linear-gradient(135deg, #4e8cff 70%, #2d3e7c 100%)',
  border: 'none',
  borderRadius: 10,
  padding: '10px 14px',
  color: '#fff',
  fontWeight: 600,
  fontSize: 15,
  boxShadow: '0 2px 8px 0 rgba(78,140,255,0.10)',
  cursor: 'pointer',
  opacity: 0.92,
  transition: 'background 0.18s, box-shadow 0.18s, opacity 0.12s',
  display: 'flex',
  alignItems: 'center',
  gap: 6,
  ':hover': {
    background: 'linear-gradient(135deg, #3578e5 80%, #1e2a43 100%)',
    boxShadow: '0 4px 16px 0 rgba(78,140,255,0.18)',
    opacity: 1,
  },
  ':disabled': {
    background: 'rgba(90,120,180,0.17)',
    color: '#b2c7e7',
    cursor: 'not-allowed',
    opacity: 0.7,
  },
});

export const FlowMenuToolbar: React.FC<{ expanded?: boolean }> = () => (
  <ToolbarContainer>
    <ToolbarButton title="Run Flow">
      <PlayArrowIcon style={{ fontSize: 24 }} />
    </ToolbarButton>
    <ToolbarButton title="Flow Settings">
      <SettingsIcon style={{ fontSize: 22 }} />
    </ToolbarButton>
    <ToolbarButton disabled title="More Actions (coming soon)">
      <MoreHorizIcon style={{ fontSize: 22 }} />
    </ToolbarButton>
  </ToolbarContainer>
);
